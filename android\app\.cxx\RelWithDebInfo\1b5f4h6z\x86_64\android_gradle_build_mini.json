{"buildFiles": ["/Users/<USER>/Documents/flutter_sdk/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Project/github/GroMart-App/gromart_customer/android/app/.cxx/RelWithDebInfo/1b5f4h6z/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Project/github/GroMart-App/gromart_customer/android/app/.cxx/RelWithDebInfo/1b5f4h6z/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}