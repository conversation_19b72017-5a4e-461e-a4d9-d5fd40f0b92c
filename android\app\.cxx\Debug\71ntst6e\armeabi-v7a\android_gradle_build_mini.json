{"buildFiles": ["/Users/<USER>/Documents/flutter/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Foodie And GroMart/GroMart/GroMart-App/gromart_customer/android/app/.cxx/Debug/71ntst6e/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Foodie And GroMart/GroMart/GroMart-App/gromart_customer/android/app/.cxx/Debug/71ntst6e/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}